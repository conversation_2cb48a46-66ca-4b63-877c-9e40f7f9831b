# Task 12: Role Creation and Management System

## Objective
Create comprehensive role creation and management functionality that is currently missing from the RBAC implementation. This addresses the gap where there is no way to create new roles in the current system.

## Priority
**UI MANAGEMENT** - Depends on Tasks 01-11

## Estimated Time
3 hours

## Dependencies
- Task 02: Permission Data Models Creation
- Task 04: Database Connection Service for Permissions
- Task 06: Core Permission Service Logic
- Task 10: Role Master Form Enhancement

## Current Problem
- No comprehensive role creation functionality in current implementation
- RoleMasterForm has basic NewRole() method but incomplete workflow
- Missing role management UI and validation
- No way for users to create custom roles

## Files to Create
- `Forms/MainForms/RoleManagementForm.cs`
- `Forms/MainForms/RoleManagementForm.Designer.cs`
- `Forms/MainForms/RoleManagementForm.resx`
- `Forms/Dialogs/RoleCreateEditDialog.cs`
- `Forms/Dialogs/RoleCreateEditDialog.Designer.cs`
- `Forms/Dialogs/RoleCreateEditDialog.resx`
- `Modules/Helpers/RoleManagement/RoleManagementHelper.cs`

## Files to Modify
- `Modules/Connections/PermissionDatabaseService.cs` (add role CRUD methods)
- `ProManage.csproj` (add new form files)

## Implementation Plan

### Phase 1: Database Service Enhancement
Add role CRUD methods to `PermissionDatabaseService.cs`:

```csharp
/// <summary>
/// Create a new role
/// </summary>
public static int CreateRole(RoleCreateRequest request)
{
    const string query = @"
        INSERT INTO roles (role_name, description, is_active)
        VALUES (@roleName, @description, @isActive)
        RETURNING role_id";
    
    try
    {
        using (var connection = DatabaseConnectionManager.Instance.CreateNewConnection())
        {
            connection.Open();
            using (var transaction = connection.BeginTransaction())
            {
                try
                {
                    using (var command = new NpgsqlCommand(query, connection, transaction))
                    {
                        command.Parameters.AddWithValue("@roleName", request.RoleName);
                        command.Parameters.AddWithValue("@description", request.Description ?? (object)DBNull.Value);
                        command.Parameters.AddWithValue("@isActive", request.IsActive);
                        
                        var roleId = (int)command.ExecuteScalar();
                        
                        // Create default permissions for all forms
                        CreateDefaultPermissionsForRole(roleId, connection, transaction);
                        
                        // Copy permissions from source role if specified
                        if (request.CopyFromRoleId.HasValue)
                        {
                            CopyRolePermissions(request.CopyFromRoleId.Value, roleId, connection, transaction);
                        }
                        
                        transaction.Commit();
                        return roleId;
                    }
                }
                catch
                {
                    transaction.Rollback();
                    throw;
                }
            }
        }
    }
    catch (Exception ex)
    {
        Debug.WriteLine($"Error creating role: {ex.Message}");
        throw;
    }
}

/// <summary>
/// Update an existing role
/// </summary>
public static bool UpdateRole(RoleUpdateRequest request)

/// <summary>
/// Delete a role (only if not in use)
/// </summary>
public static bool DeleteRole(int roleId)

/// <summary>
/// Check if role is currently assigned to users
/// </summary>
public static bool IsRoleInUse(int roleId)

/// <summary>
/// Get all roles with user count
/// </summary>
public static List<RoleWithUserCount> GetAllRolesWithUserCount()

/// <summary>
/// Copy permissions from one role to another
/// </summary>
public static bool CopyRolePermissions(int sourceRoleId, int targetRoleId)
```

### Phase 2: RoleManagementForm
Create main role management interface:

#### Form Features:
- MDI child form with ribbon interface
- Role listing grid with columns:
  - Role Name
  - Description
  - Active Status
  - Created Date
  - User Count
- Ribbon buttons:
  - New Role
  - Edit Role
  - Delete Role
  - Copy Permissions
  - Refresh
- Double-click row to edit role
- Context menu with operations

#### Grid Setup:
```csharp
private void SetupRoleGrid()
{
    gridViewRoles.OptionsView.ShowGroupPanel = false;
    gridViewRoles.OptionsSelection.EnableAppearanceFocusedCell = false;
    gridViewRoles.OptionsCustomization.AllowColumnMoving = false;
    
    // Setup columns
    var colRoleName = gridViewRoles.Columns.Add();
    colRoleName.FieldName = "RoleName";
    colRoleName.Caption = "Role Name";
    colRoleName.Width = 150;
    
    var colDescription = gridViewRoles.Columns.Add();
    colDescription.FieldName = "Description";
    colDescription.Caption = "Description";
    colDescription.Width = 250;
    
    var colIsActive = gridViewRoles.Columns.Add();
    colIsActive.FieldName = "IsActive";
    colIsActive.Caption = "Active";
    colIsActive.Width = 80;
    colIsActive.ColumnEdit = new CheckEdit();
    
    var colUserCount = gridViewRoles.Columns.Add();
    colUserCount.FieldName = "UserCount";
    colUserCount.Caption = "Users";
    colUserCount.Width = 80;
    colUserCount.AppearanceCell.TextOptions.HAlignment = HorzAlignment.Center;
    
    var colCreatedDate = gridViewRoles.Columns.Add();
    colCreatedDate.FieldName = "CreatedDate";
    colCreatedDate.Caption = "Created";
    colCreatedDate.Width = 120;
    colCreatedDate.DisplayFormat.FormatType = FormatType.DateTime;
    colCreatedDate.DisplayFormat.FormatString = "dd/MM/yyyy";
}
```

### Phase 3: RoleCreateEditDialog
Create role creation/editing dialog:

#### Dialog Features:
- Modal dialog (450x350)
- Fields:
  - Role Name (required, unique validation)
  - Description (optional, multi-line)
  - Active checkbox
- Initial permissions options:
  - No permissions (recommended)
  - Copy from existing role
- Save/Cancel buttons
- Real-time validation

#### Validation Rules:
- Role names must be unique (case-insensitive)
- Role names cannot be empty or whitespace
- Cannot modify system roles (Administrator, Manager, User, ReadOnly)
- Description limited to 500 characters

### Phase 4: RoleManagementHelper
Create helper class for role operations:

```csharp
public static class RoleManagementHelper
{
    /// <summary>
    /// Validate role name for uniqueness and format
    /// </summary>
    public static ValidationResult ValidateRoleName(string roleName, int? excludeRoleId = null)
    
    /// <summary>
    /// Check if role can be deleted
    /// </summary>
    public static bool CanDeleteRole(int roleId)
    
    /// <summary>
    /// Get user count for role
    /// </summary>
    public static int GetRoleUserCount(int roleId)
    
    /// <summary>
    /// Check if role is system role
    /// </summary>
    public static bool IsSystemRole(string roleName)
}
```

## Acceptance Criteria
- [ ] RoleManagementForm with role listing grid (Name, Description, Active, Created Date, User Count)
- [ ] Role CRUD operations: Create, Edit, Delete, Copy Permissions
- [ ] RoleCreateEditDialog for role creation/editing with validation
- [ ] Role name uniqueness validation
- [ ] Prevent deletion of roles currently assigned to users
- [ ] Automatic permission setup for new roles (default to no permissions)
- [ ] Copy permissions from existing role functionality
- [ ] Integration with existing permission system
- [ ] MDI child form with ribbon interface
- [ ] Proper error handling and user feedback
- [ ] Cannot delete system roles (Administrator, Manager, User, ReadOnly)
- [ ] Real-time validation in dialog
- [ ] Context menu support in grid
- [ ] Double-click to edit functionality

## Integration Points
- Add ribbon button in MainFrame to open RoleManagementForm
- Integrate with existing permission cache clearing
- Update role dropdowns in other forms when roles are added/modified
- Ensure permission system recognizes new roles immediately

## Error Handling
- Graceful handling of database connection issues
- User-friendly error messages for validation failures
- Prevention of system lockout scenarios
- Proper transaction rollback on failures
- Audit trail for role creation/modification/deletion

## Testing Scenarios
1. Create new role with no permissions
2. Create new role copying from existing role
3. Edit existing role details
4. Attempt to delete role in use (should fail)
5. Delete unused role (should succeed)
6. Validate role name uniqueness
7. Test system role protection
8. Verify permission copying works correctly
9. Test grid refresh after operations
10. Verify integration with permission system

This task fills the critical gap in role management functionality and provides a complete solution for role creation and management within the RBAC system.
