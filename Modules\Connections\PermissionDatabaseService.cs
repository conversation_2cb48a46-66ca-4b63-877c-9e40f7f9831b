using System;
using System.Collections.Generic;
using System.Data;
using System.Diagnostics;
using System.Linq;
using Npgsql;
using ProManage.Modules.Models;

namespace ProManage.Modules.Connections
{
    /// <summary>
    /// Specialized database service for RBAC permission operations.
    /// Handles all database interactions for roles, permissions, and user access control.
    /// Follows ProManage's centralized database architecture using PostgreSQL.
    /// </summary>
    public class PermissionDatabaseService
    {
        /// <summary>
        /// Get all active roles
        /// </summary>
        /// <returns>List of active roles</returns>
        public static List<Role> GetAllRoles()
        {
            const string query = @"
                SELECT role_id, role_name, description, is_active, created_date, modified_date 
                FROM roles 
                WHERE is_active = true 
                ORDER BY role_name";
            
            var roles = new List<Role>();
            
            try
            {
                using (var connection = DatabaseConnectionManager.Instance.CreateNewConnection())
                {
                    connection.Open();
                    using (var command = new NpgsqlCommand(query, connection))
                    {
                        using (var reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                roles.Add(new Role
                                {
                                    RoleId = reader.GetInt32("role_id"),
                                    RoleName = reader.GetString("role_name"),
                                    Description = reader.IsDBNull(reader.GetOrdinal("description")) ? null : reader.GetString("description"),
                                    IsActive = reader.GetBoolean("is_active"),
                                    CreatedDate = reader.GetDateTime("created_date"),
                                    ModifiedDate = reader.GetDateTime("modified_date")
                                });
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error getting all roles: {ex.Message}");
                throw;
            }
            
            return roles;
        }
        
        /// <summary>
        /// Get role by ID
        /// </summary>
        /// <param name="roleId">Role ID to find</param>
        /// <returns>Role object or null if not found</returns>
        public static Role GetRoleById(int roleId)
        {
            const string query = @"
                SELECT role_id, role_name, description, is_active, created_date, modified_date 
                FROM roles 
                WHERE role_id = @roleId";
            
            try
            {
                using (var connection = DatabaseConnectionManager.Instance.CreateNewConnection())
                {
                    connection.Open();
                    using (var command = new NpgsqlCommand(query, connection))
                    {
                        command.Parameters.AddWithValue("@roleId", roleId);
                        
                        using (var reader = command.ExecuteReader())
                        {
                            if (reader.Read())
                            {
                                return new Role
                                {
                                    RoleId = reader.GetInt32("role_id"),
                                    RoleName = reader.GetString("role_name"),
                                    Description = reader.IsDBNull(reader.GetOrdinal("description")) ? null : reader.GetString("description"),
                                    IsActive = reader.GetBoolean("is_active"),
                                    CreatedDate = reader.GetDateTime("created_date"),
                                    ModifiedDate = reader.GetDateTime("modified_date")
                                };
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error getting role by ID {roleId}: {ex.Message}");
                throw;
            }
            
            return null;
        }
        
        /// <summary>
        /// Get role permissions for specific role
        /// </summary>
        /// <param name="roleId">Role ID to get permissions for</param>
        /// <returns>List of role permissions</returns>
        public static List<RolePermission> GetRolePermissions(int roleId)
        {
            const string query = @"
                SELECT permission_id, role_id, form_name, read_permission, new_permission, 
                       edit_permission, delete_permission, print_permission, created_date
                FROM role_permissions 
                WHERE role_id = @roleId 
                ORDER BY form_name";
            
            var permissions = new List<RolePermission>();
            
            try
            {
                using (var connection = DatabaseConnectionManager.Instance.CreateNewConnection())
                {
                    connection.Open();
                    using (var command = new NpgsqlCommand(query, connection))
                    {
                        command.Parameters.AddWithValue("@roleId", roleId);
                        
                        using (var reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                permissions.Add(new RolePermission
                                {
                                    PermissionId = reader.GetInt32("permission_id"),
                                    RoleId = reader.GetInt32("role_id"),
                                    FormName = reader.GetString("form_name"),
                                    ReadPermission = reader.GetBoolean("read_permission"),
                                    NewPermission = reader.GetBoolean("new_permission"),
                                    EditPermission = reader.GetBoolean("edit_permission"),
                                    DeletePermission = reader.GetBoolean("delete_permission"),
                                    PrintPermission = reader.GetBoolean("print_permission"),
                                    CreatedDate = reader.GetDateTime("created_date")
                                });
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error getting role permissions for role {roleId}: {ex.Message}");
                throw;
            }
            
            return permissions;
        }
        
        /// <summary>
        /// Get specific role permission
        /// </summary>
        /// <param name="roleId">Role ID</param>
        /// <param name="formName">Form name</param>
        /// <returns>Role permission or null if not found</returns>
        public static RolePermission GetRolePermission(int roleId, string formName)
        {
            const string query = @"
                SELECT permission_id, role_id, form_name, read_permission, new_permission, 
                       edit_permission, delete_permission, print_permission, created_date
                FROM role_permissions 
                WHERE role_id = @roleId AND form_name = @formName";
            
            try
            {
                using (var connection = DatabaseConnectionManager.Instance.CreateNewConnection())
                {
                    connection.Open();
                    using (var command = new NpgsqlCommand(query, connection))
                    {
                        command.Parameters.AddWithValue("@roleId", roleId);
                        command.Parameters.AddWithValue("@formName", formName);
                        
                        using (var reader = command.ExecuteReader())
                        {
                            if (reader.Read())
                            {
                                return new RolePermission
                                {
                                    PermissionId = reader.GetInt32("permission_id"),
                                    RoleId = reader.GetInt32("role_id"),
                                    FormName = reader.GetString("form_name"),
                                    ReadPermission = reader.GetBoolean("read_permission"),
                                    NewPermission = reader.GetBoolean("new_permission"),
                                    EditPermission = reader.GetBoolean("edit_permission"),
                                    DeletePermission = reader.GetBoolean("delete_permission"),
                                    PrintPermission = reader.GetBoolean("print_permission"),
                                    CreatedDate = reader.GetDateTime("created_date")
                                };
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error getting role permission for role {roleId}, form {formName}: {ex.Message}");
                throw;
            }
            
            return null;
        }
        
        /// <summary>
        /// Update role permissions in batch
        /// </summary>
        /// <param name="updates">List of permission updates</param>
        /// <returns>True if successful, false otherwise</returns>
        public static bool UpdateRolePermissions(List<RolePermissionUpdate> updates)
        {
            try
            {
                using (var connection = DatabaseConnectionManager.Instance.CreateNewConnection())
                {
                    connection.Open();
                    using (var transaction = connection.BeginTransaction())
                    {
                        try
                        {
                            foreach (var update in updates)
                            {
                                const string query = @"
                                    UPDATE role_permissions 
                                    SET read_permission = @readPermission,
                                        new_permission = @newPermission,
                                        edit_permission = @editPermission,
                                        delete_permission = @deletePermission,
                                        print_permission = @printPermission
                                    WHERE role_id = @roleId AND form_name = @formName";
                                
                                using (var command = new NpgsqlCommand(query, connection, transaction))
                                {
                                    command.Parameters.AddWithValue("@roleId", update.RoleId);
                                    command.Parameters.AddWithValue("@formName", update.FormName);
                                    command.Parameters.AddWithValue("@readPermission", update.ReadPermission);
                                    command.Parameters.AddWithValue("@newPermission", update.NewPermission);
                                    command.Parameters.AddWithValue("@editPermission", update.EditPermission);
                                    command.Parameters.AddWithValue("@deletePermission", update.DeletePermission);
                                    command.Parameters.AddWithValue("@printPermission", update.PrintPermission);
                                    
                                    command.ExecuteNonQuery();
                                }
                            }
                            
                            transaction.Commit();
                            return true;
                        }
                        catch
                        {
                            transaction.Rollback();
                            throw;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error updating role permissions: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Get user permissions (overrides only)
        /// </summary>
        /// <param name="userId">User ID to get permissions for</param>
        /// <returns>List of user permission overrides</returns>
        public static List<UserPermission> GetUserPermissions(int userId)
        {
            const string query = @"
                SELECT user_permission_id, user_id, form_name, read_permission, new_permission,
                       edit_permission, delete_permission, print_permission, created_date
                FROM user_permissions
                WHERE user_id = @userId
                ORDER BY form_name";

            var permissions = new List<UserPermission>();

            try
            {
                using (var connection = DatabaseConnectionManager.Instance.CreateNewConnection())
                {
                    connection.Open();
                    using (var command = new NpgsqlCommand(query, connection))
                    {
                        command.Parameters.AddWithValue("@userId", userId);

                        using (var reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                permissions.Add(new UserPermission
                                {
                                    UserPermissionId = reader.GetInt32("user_permission_id"),
                                    UserId = reader.GetInt32("user_id"),
                                    FormName = reader.GetString("form_name"),
                                    ReadPermission = reader.IsDBNull(reader.GetOrdinal("read_permission")) ? (bool?)null : reader.GetBoolean("read_permission"),
                                    NewPermission = reader.IsDBNull(reader.GetOrdinal("new_permission")) ? (bool?)null : reader.GetBoolean("new_permission"),
                                    EditPermission = reader.IsDBNull(reader.GetOrdinal("edit_permission")) ? (bool?)null : reader.GetBoolean("edit_permission"),
                                    DeletePermission = reader.IsDBNull(reader.GetOrdinal("delete_permission")) ? (bool?)null : reader.GetBoolean("delete_permission"),
                                    PrintPermission = reader.IsDBNull(reader.GetOrdinal("print_permission")) ? (bool?)null : reader.GetBoolean("print_permission"),
                                    CreatedDate = reader.GetDateTime("created_date")
                                });
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error getting user permissions for user {userId}: {ex.Message}");
                throw;
            }

            return permissions;
        }

        /// <summary>
        /// Get specific user permission
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <param name="formName">Form name</param>
        /// <returns>User permission or null if not found</returns>
        public static UserPermission GetUserPermission(int userId, string formName)
        {
            const string query = @"
                SELECT user_permission_id, user_id, form_name, read_permission, new_permission,
                       edit_permission, delete_permission, print_permission, created_date
                FROM user_permissions
                WHERE user_id = @userId AND form_name = @formName";

            try
            {
                using (var connection = DatabaseConnectionManager.Instance.CreateNewConnection())
                {
                    connection.Open();
                    using (var command = new NpgsqlCommand(query, connection))
                    {
                        command.Parameters.AddWithValue("@userId", userId);
                        command.Parameters.AddWithValue("@formName", formName);

                        using (var reader = command.ExecuteReader())
                        {
                            if (reader.Read())
                            {
                                return new UserPermission
                                {
                                    UserPermissionId = reader.GetInt32("user_permission_id"),
                                    UserId = reader.GetInt32("user_id"),
                                    FormName = reader.GetString("form_name"),
                                    ReadPermission = reader.IsDBNull(reader.GetOrdinal("read_permission")) ? (bool?)null : reader.GetBoolean("read_permission"),
                                    NewPermission = reader.IsDBNull(reader.GetOrdinal("new_permission")) ? (bool?)null : reader.GetBoolean("new_permission"),
                                    EditPermission = reader.IsDBNull(reader.GetOrdinal("edit_permission")) ? (bool?)null : reader.GetBoolean("edit_permission"),
                                    DeletePermission = reader.IsDBNull(reader.GetOrdinal("delete_permission")) ? (bool?)null : reader.GetBoolean("delete_permission"),
                                    PrintPermission = reader.IsDBNull(reader.GetOrdinal("print_permission")) ? (bool?)null : reader.GetBoolean("print_permission"),
                                    CreatedDate = reader.GetDateTime("created_date")
                                });
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error getting user permission for user {userId}, form {formName}: {ex.Message}");
                throw;
            }

            return null;
        }

        /// <summary>
        /// Get global permissions for user
        /// </summary>
        /// <param name="userId">User ID to get global permissions for</param>
        /// <returns>Global permission object or null if not found</returns>
        public static GlobalPermission GetGlobalPermissions(int userId)
        {
            const string query = @"
                SELECT global_permission_id, user_id, can_create_users, can_edit_users,
                       can_delete_users, can_print_users, created_date
                FROM global_permissions
                WHERE user_id = @userId";

            try
            {
                using (var connection = DatabaseConnectionManager.Instance.CreateNewConnection())
                {
                    connection.Open();
                    using (var command = new NpgsqlCommand(query, connection))
                    {
                        command.Parameters.AddWithValue("@userId", userId);

                        using (var reader = command.ExecuteReader())
                        {
                            if (reader.Read())
                            {
                                return new GlobalPermission
                                {
                                    GlobalPermissionId = reader.GetInt32("global_permission_id"),
                                    UserId = reader.GetInt32("user_id"),
                                    CanCreateUsers = reader.GetBoolean("can_create_users"),
                                    CanEditUsers = reader.GetBoolean("can_edit_users"),
                                    CanDeleteUsers = reader.GetBoolean("can_delete_users"),
                                    CanPrintUsers = reader.GetBoolean("can_print_users"),
                                    CreatedDate = reader.GetDateTime("created_date")
                                };
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error getting global permissions for user {userId}: {ex.Message}");
                throw;
            }

            return null;
        }

        /// <summary>
        /// Add form permissions for all roles
        /// </summary>
        /// <param name="formName">Name of the form to add</param>
        /// <returns>True if successful, false otherwise</returns>
        public static bool AddFormToPermissionSystem(string formName)
        {
            const string query = @"
                INSERT INTO role_permissions (role_id, form_name, read_permission, new_permission, edit_permission, delete_permission, print_permission)
                SELECT role_id, @formName, false, false, false, false, false
                FROM roles
                WHERE NOT EXISTS (
                    SELECT 1 FROM role_permissions
                    WHERE role_id = roles.role_id AND form_name = @formName
                )";

            try
            {
                using (var connection = DatabaseConnectionManager.Instance.CreateNewConnection())
                {
                    connection.Open();
                    using (var command = new NpgsqlCommand(query, connection))
                    {
                        command.Parameters.AddWithValue("@formName", formName);
                        return command.ExecuteNonQuery() > 0;
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error adding form {formName} to permission system: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Remove form from permission system
        /// </summary>
        /// <param name="formName">Name of the form to remove</param>
        /// <returns>True if successful, false otherwise</returns>
        public static bool RemoveFormFromPermissionSystem(string formName)
        {
            try
            {
                using (var connection = DatabaseConnectionManager.Instance.CreateNewConnection())
                {
                    connection.Open();
                    using (var transaction = connection.BeginTransaction())
                    {
                        try
                        {
                            // Remove user permissions first
                            const string deleteUserPerms = "DELETE FROM user_permissions WHERE form_name = @formName";
                            using (var command = new NpgsqlCommand(deleteUserPerms, connection, transaction))
                            {
                                command.Parameters.AddWithValue("@formName", formName);
                                command.ExecuteNonQuery();
                            }

                            // Remove role permissions
                            const string deleteRolePerms = "DELETE FROM role_permissions WHERE form_name = @formName";
                            using (var command = new NpgsqlCommand(deleteRolePerms, connection, transaction))
                            {
                                command.Parameters.AddWithValue("@formName", formName);
                                command.ExecuteNonQuery();
                            }

                            transaction.Commit();
                            return true;
                        }
                        catch
                        {
                            transaction.Rollback();
                            throw;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error removing form {formName} from permission system: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Update user permission overrides
        /// </summary>
        /// <param name="updates">List of user permission updates</param>
        /// <returns>True if successful, false otherwise</returns>
        public static bool UpdateUserPermissions(List<UserPermissionUpdate> updates)
        {
            try
            {
                using (var connection = DatabaseConnectionManager.Instance.CreateNewConnection())
                {
                    connection.Open();
                    using (var transaction = connection.BeginTransaction())
                    {
                        try
                        {
                            foreach (var update in updates)
                            {
                                const string query = @"
                                    INSERT INTO user_permissions (user_id, form_name, read_permission, new_permission, edit_permission, delete_permission, print_permission)
                                    VALUES (@userId, @formName, @readPermission, @newPermission, @editPermission, @deletePermission, @printPermission)
                                    ON CONFLICT (user_id, form_name)
                                    DO UPDATE SET
                                        read_permission = @readPermission,
                                        new_permission = @newPermission,
                                        edit_permission = @editPermission,
                                        delete_permission = @deletePermission,
                                        print_permission = @printPermission";

                                using (var command = new NpgsqlCommand(query, connection, transaction))
                                {
                                    command.Parameters.AddWithValue("@userId", update.UserId);
                                    command.Parameters.AddWithValue("@formName", update.FormName);
                                    command.Parameters.AddWithValue("@readPermission", (object)update.ReadPermission ?? DBNull.Value);
                                    command.Parameters.AddWithValue("@newPermission", (object)update.NewPermission ?? DBNull.Value);
                                    command.Parameters.AddWithValue("@editPermission", (object)update.EditPermission ?? DBNull.Value);
                                    command.Parameters.AddWithValue("@deletePermission", (object)update.DeletePermission ?? DBNull.Value);
                                    command.Parameters.AddWithValue("@printPermission", (object)update.PrintPermission ?? DBNull.Value);

                                    command.ExecuteNonQuery();
                                }
                            }

                            transaction.Commit();
                            Debug.WriteLine($"Updated {updates.Count} user permission records");
                            return true;
                        }
                        catch (Exception ex)
                        {
                            transaction.Rollback();
                            Debug.WriteLine($"Error updating user permissions: {ex.Message}");
                            throw;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error updating user permissions: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Get user with role information
        /// </summary>
        /// <param name="userId">User ID to get role information for</param>
        /// <returns>User with role information or null if not found</returns>
        public static UserWithRole GetUserWithRole(int userId)
        {
            const string query = @"
                SELECT u.user_id, u.role_id, r.role_name
                FROM users u
                LEFT JOIN roles r ON u.role_id = r.role_id
                WHERE u.user_id = @userId";

            try
            {
                using (var connection = DatabaseConnectionManager.Instance.CreateNewConnection())
                {
                    connection.Open();
                    using (var command = new NpgsqlCommand(query, connection))
                    {
                        command.Parameters.AddWithValue("@userId", userId);

                        using (var reader = command.ExecuteReader())
                        {
                            if (reader.Read())
                            {
                                var roleId = reader.IsDBNull("role_id") ? (int?)null : reader.GetInt32("role_id");

                                return new UserWithRole
                                {
                                    UserId = reader.GetInt32("user_id"),
                                    RoleId = roleId ?? 0, // Default to 0 if no role assigned
                                    RoleName = reader.IsDBNull("role_name") ? null : reader.GetString("role_name")
                                };
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error getting user with role for user {userId}: {ex.Message}");
                throw;
            }

            return null;
        }

        /// <summary>
        /// Update global permissions for a user
        /// </summary>
        /// <param name="update">Global permission update</param>
        /// <returns>True if successful, false otherwise</returns>
        public static bool UpdateGlobalPermissions(GlobalPermissionUpdate update)
        {
            try
            {
                using (var connection = DatabaseConnectionManager.Instance.CreateNewConnection())
                {
                    connection.Open();

                    const string query = @"
                        INSERT INTO global_permissions (user_id, can_create_users, can_edit_users, can_delete_users, can_print_users)
                        VALUES (@userId, @canCreateUsers, @canEditUsers, @canDeleteUsers, @canPrintUsers)
                        ON CONFLICT (user_id)
                        DO UPDATE SET
                            can_create_users = @canCreateUsers,
                            can_edit_users = @canEditUsers,
                            can_delete_users = @canDeleteUsers,
                            can_print_users = @canPrintUsers";

                    using (var command = new NpgsqlCommand(query, connection))
                    {
                        command.Parameters.AddWithValue("@userId", update.UserId);
                        command.Parameters.AddWithValue("@canCreateUsers", update.CanCreateUsers);
                        command.Parameters.AddWithValue("@canEditUsers", update.CanEditUsers);
                        command.Parameters.AddWithValue("@canDeleteUsers", update.CanDeleteUsers);
                        command.Parameters.AddWithValue("@canPrintUsers", update.CanPrintUsers);

                        int rowsAffected = command.ExecuteNonQuery();
                        Debug.WriteLine($"Updated global permissions for user {update.UserId}, {rowsAffected} rows affected");
                        return rowsAffected > 0;
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error updating global permissions for user {update.UserId}: {ex.Message}");
                return false;
            }
        }

        #region User Permission Override Operations

        /// <summary>
        /// Remove user permission override (revert to role permission)
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <param name="formName">Form name</param>
        /// <returns>True if successful, false otherwise</returns>
        public static bool RemoveUserPermissionOverride(int userId, string formName)
        {
            const string query = "DELETE FROM user_permissions WHERE user_id = @userId AND form_name = @formName";

            try
            {
                using (var connection = DatabaseConnectionManager.Instance.CreateNewConnection())
                {
                    connection.Open();
                    using (var command = new NpgsqlCommand(query, connection))
                    {
                        command.Parameters.AddWithValue("@userId", userId);
                        command.Parameters.AddWithValue("@formName", formName);

                        int rowsAffected = command.ExecuteNonQuery();
                        Debug.WriteLine($"Removed user permission override for user {userId}, form {formName}");
                        return rowsAffected > 0;
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error removing user permission override for user {userId}, form {formName}: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Remove all user permission overrides for a user
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <returns>True if successful, false otherwise</returns>
        public static bool RemoveAllUserPermissionOverrides(int userId)
        {
            const string query = "DELETE FROM user_permissions WHERE user_id = @userId";

            try
            {
                using (var connection = DatabaseConnectionManager.Instance.CreateNewConnection())
                {
                    connection.Open();
                    using (var command = new NpgsqlCommand(query, connection))
                    {
                        command.Parameters.AddWithValue("@userId", userId);
                        int rowsAffected = command.ExecuteNonQuery();
                        Debug.WriteLine($"Removed {rowsAffected} user permission overrides for user {userId}");
                        return true; // Success even if 0 rows affected (no overrides to remove)
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error removing all user permission overrides for user {userId}: {ex.Message}");
                return false;
            }
        }

        #endregion

        #region Global Permission Operations

        /// <summary>
        /// Remove global permissions for user
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <returns>True if successful, false otherwise</returns>
        public static bool RemoveGlobalPermissions(int userId)
        {
            const string query = "DELETE FROM global_permissions WHERE user_id = @userId";

            try
            {
                using (var connection = DatabaseConnectionManager.Instance.CreateNewConnection())
                {
                    connection.Open();
                    using (var command = new NpgsqlCommand(query, connection))
                    {
                        command.Parameters.AddWithValue("@userId", userId);
                        int rowsAffected = command.ExecuteNonQuery();
                        Debug.WriteLine($"Removed global permissions for user {userId}");
                        return rowsAffected >= 0; // Success even if 0 rows affected
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error removing global permissions for user {userId}: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Reset user permissions to role defaults (remove all user overrides)
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <returns>True if successful, false otherwise</returns>
        public static bool ResetUserPermissions(int userId)
        {
            const string query = @"DELETE FROM user_permissions WHERE user_id = @userId";

            try
            {
                using (var connection = DatabaseConnectionManager.Instance.CreateNewConnection())
                {
                    connection.Open();
                    using (var command = new NpgsqlCommand(query, connection))
                    {
                        command.Parameters.AddWithValue("@userId", userId);
                        var rowsAffected = command.ExecuteNonQuery();
                        Debug.WriteLine($"Reset permissions for user {userId}, {rowsAffected} rows affected");
                        return true;
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error resetting user permissions: {ex.Message}");
                return false;
            }
        }

        #endregion

        #region Bulk Operations

        /// <summary>
        /// Get all active users
        /// </summary>
        /// <returns>List of active users</returns>
        public static List<UserInfo> GetAllUsers()
        {
            const string query = @"
                SELECT u.user_id, u.username, u.full_name, u.role_id, r.role_name, u.is_active
                FROM users u
                LEFT JOIN roles r ON u.role_id = r.role_id
                WHERE u.is_active = true
                ORDER BY u.username";

            var users = new List<UserInfo>();

            try
            {
                using (var connection = DatabaseConnectionManager.Instance.CreateNewConnection())
                {
                    connection.Open();
                    using (var command = new NpgsqlCommand(query, connection))
                    {
                        using (var reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                users.Add(new UserInfo
                                {
                                    UserId = reader.GetInt32("user_id"),
                                    Username = reader.GetString("username"),
                                    FullName = reader.IsDBNull(reader.GetOrdinal("full_name")) ? null : reader.GetString("full_name"),
                                    RoleId = reader.IsDBNull(reader.GetOrdinal("role_id")) ? 0 : reader.GetInt32("role_id"),
                                    RoleName = reader.IsDBNull(reader.GetOrdinal("role_name")) ? null : reader.GetString("role_name"),
                                    IsActive = reader.GetBoolean("is_active")
                                });
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error getting all users: {ex.Message}");
                throw;
            }

            return users;
        }

        /// <summary>
        /// Get all users with their effective permissions
        /// </summary>
        /// <returns>List of users with permissions</returns>
        public static List<UserWithPermissions> GetAllUsersWithPermissions()
        {
            const string query = @"
                SELECT u.user_id, u.username, u.full_name, u.role_id, r.role_name
                FROM users u
                LEFT JOIN roles r ON u.role_id = r.role_id
                WHERE u.is_active = true
                ORDER BY u.username";

            var users = new List<UserWithPermissions>();

            try
            {
                using (var connection = DatabaseConnectionManager.Instance.CreateNewConnection())
                {
                    connection.Open();
                    using (var command = new NpgsqlCommand(query, connection))
                    {
                        using (var reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                var user = new UserWithPermissions
                                {
                                    UserId = reader.GetInt32("user_id"),
                                    Username = reader.GetString("username"),
                                    FullName = reader.IsDBNull("full_name") ? null : reader.GetString("full_name"),
                                    RoleId = reader.IsDBNull("role_id") ? 0 : reader.GetInt32("role_id"),
                                    RoleName = reader.IsDBNull("role_name") ? null : reader.GetString("role_name")
                                };

                                users.Add(user);
                            }
                        }
                    }

                    // Load permissions for each user
                    foreach (var user in users)
                    {
                        user.FormPermissions = GetEffectivePermissionsForUser(user.UserId, user.RoleId);
                        user.GlobalPermissions = GetGlobalPermissions(user.UserId);
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error getting all users with permissions: {ex.Message}");
                throw;
            }

            return users;
        }

        /// <summary>
        /// Get effective permissions for a specific user
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <param name="roleId">User's role ID</param>
        /// <returns>List of effective permissions</returns>
        private static List<EffectivePermission> GetEffectivePermissionsForUser(int userId, int roleId)
        {
            var effectivePermissions = new List<EffectivePermission>();

            try
            {
                // Get all forms from role permissions
                var rolePermissions = GetRolePermissions(roleId);
                var userPermissions = GetUserPermissions(userId);

                foreach (var rolePermission in rolePermissions)
                {
                    var userOverride = userPermissions.FirstOrDefault(up => up.FormName == rolePermission.FormName);

                    var effective = new EffectivePermission
                    {
                        FormName = rolePermission.FormName,
                        ReadPermission = userOverride?.ReadPermission ?? rolePermission.ReadPermission,
                        NewPermission = userOverride?.NewPermission ?? rolePermission.NewPermission,
                        EditPermission = userOverride?.EditPermission ?? rolePermission.EditPermission,
                        DeletePermission = userOverride?.DeletePermission ?? rolePermission.DeletePermission,
                        PrintPermission = userOverride?.PrintPermission ?? rolePermission.PrintPermission,
                        Source = userOverride != null ? PermissionSource.UserOverride : PermissionSource.Role
                    };

                    effectivePermissions.Add(effective);
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error getting effective permissions for user {userId}: {ex.Message}");
            }

            return effectivePermissions;
        }

        /// <summary>
        /// Copy permissions from one role to another
        /// </summary>
        /// <param name="sourceRoleId">Source role ID</param>
        /// <param name="targetRoleId">Target role ID</param>
        /// <returns>True if successful, false otherwise</returns>
        public static bool CopyRolePermissions(int sourceRoleId, int targetRoleId)
        {
            try
            {
                using (var connection = DatabaseConnectionManager.Instance.CreateNewConnection())
                {
                    connection.Open();
                    using (var transaction = connection.BeginTransaction())
                    {
                        try
                        {
                            // First, delete existing permissions for target role
                            const string deleteQuery = "DELETE FROM role_permissions WHERE role_id = @targetRoleId";
                            using (var deleteCommand = new NpgsqlCommand(deleteQuery, connection, transaction))
                            {
                                deleteCommand.Parameters.AddWithValue("@targetRoleId", targetRoleId);
                                deleteCommand.ExecuteNonQuery();
                            }

                            // Copy permissions from source role
                            const string copyQuery = @"
                                INSERT INTO role_permissions (role_id, form_name, read_permission, new_permission, edit_permission, delete_permission, print_permission)
                                SELECT @targetRoleId, form_name, read_permission, new_permission, edit_permission, delete_permission, print_permission
                                FROM role_permissions
                                WHERE role_id = @sourceRoleId";

                            using (var copyCommand = new NpgsqlCommand(copyQuery, connection, transaction))
                            {
                                copyCommand.Parameters.AddWithValue("@sourceRoleId", sourceRoleId);
                                copyCommand.Parameters.AddWithValue("@targetRoleId", targetRoleId);
                                int copiedRows = copyCommand.ExecuteNonQuery();
                                Debug.WriteLine($"Copied {copiedRows} permissions from role {sourceRoleId} to role {targetRoleId}");
                            }

                            transaction.Commit();
                            return true;
                        }
                        catch
                        {
                            transaction.Rollback();
                            throw;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error copying role permissions from {sourceRoleId} to {targetRoleId}: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Reset user to role permissions (remove all overrides)
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <returns>True if successful, false otherwise</returns>
        public static bool ResetUserToRolePermissions(int userId)
        {
            return RemoveAllUserPermissionOverrides(userId);
        }

        #endregion

        #region Reporting and Analytics

        /// <summary>
        /// Get permission summary for reporting
        /// </summary>
        /// <returns>Permission summary statistics</returns>
        public static PermissionSummary GetPermissionSummary()
        {
            const string query = @"
                SELECT
                    (SELECT COUNT(*) FROM roles WHERE is_active = true) as ActiveRoles,
                    (SELECT COUNT(*) FROM users WHERE is_active = true) as ActiveUsers,
                    (SELECT COUNT(DISTINCT form_name) FROM role_permissions) as FormsInSystem,
                    (SELECT COUNT(*) FROM user_permissions) as UserOverrides,
                    (SELECT COUNT(*) FROM global_permissions) as UsersWithGlobalPermissions";

            try
            {
                using (var connection = DatabaseConnectionManager.Instance.CreateNewConnection())
                {
                    connection.Open();
                    using (var command = new NpgsqlCommand(query, connection))
                    {
                        using (var reader = command.ExecuteReader())
                        {
                            if (reader.Read())
                            {
                                return new PermissionSummary
                                {
                                    ActiveRoles = reader.GetInt32("activeroles"),
                                    ActiveUsers = reader.GetInt32("activeusers"),
                                    FormsInSystem = reader.GetInt32("formsinsystem"),
                                    UserOverrides = reader.GetInt32("useroverrides"),
                                    UsersWithGlobalPermissions = reader.GetInt32("userswithglobalpermissions")
                                };
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error getting permission summary: {ex.Message}");
                throw;
            }

            return new PermissionSummary();
        }

        /// <summary>
        /// Get users without any permissions
        /// </summary>
        /// <returns>List of user IDs without permissions</returns>
        public static List<int> GetUsersWithoutPermissions()
        {
            const string query = @"
                SELECT u.user_id
                FROM users u
                LEFT JOIN roles r ON u.role_id = r.role_id
                WHERE u.is_active = true
                AND (u.role_id IS NULL OR r.is_active = false)";

            var userIds = new List<int>();

            try
            {
                using (var connection = DatabaseConnectionManager.Instance.CreateNewConnection())
                {
                    connection.Open();
                    using (var command = new NpgsqlCommand(query, connection))
                    {
                        using (var reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                userIds.Add(reader.GetInt32("user_id"));
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error getting users without permissions: {ex.Message}");
                throw;
            }

            return userIds;
        }

        #endregion
    }
}
