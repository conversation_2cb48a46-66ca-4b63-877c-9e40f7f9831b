namespace ProManage.Forms
{
    partial class PermissionManagementForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.tabControlMain = new DevExpress.XtraTab.XtraTabControl();
            this.tabPageRolePermissions = new DevExpress.XtraTab.XtraTabPage();
            this.gridControlRolePermissions = new DevExpress.XtraGrid.GridControl();
            this.gridViewRolePermissions = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.panelRoleSelection = new DevExpress.XtraEditors.PanelControl();
            this.cmbRoles = new DevExpress.XtraEditors.ComboBoxEdit();
            this.labelControl1 = new DevExpress.XtraEditors.LabelControl();
            this.tabPageUserPermissions = new DevExpress.XtraTab.XtraTabPage();
            this.gridControlUserPermissions = new DevExpress.XtraGrid.GridControl();
            this.gridViewUserPermissions = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.panelUserSelection = new DevExpress.XtraEditors.PanelControl();
            this.btnCopyFromRole = new DevExpress.XtraEditors.SimpleButton();
            this.btnResetUserPermissions = new DevExpress.XtraEditors.SimpleButton();
            this.cmbUsers = new DevExpress.XtraEditors.ComboBoxEdit();
            this.labelControl2 = new DevExpress.XtraEditors.LabelControl();
            this.panelButtons = new DevExpress.XtraEditors.PanelControl();
            this.btnRefresh = new DevExpress.XtraEditors.SimpleButton();
            this.btnCancel = new DevExpress.XtraEditors.SimpleButton();
            this.btnSave = new DevExpress.XtraEditors.SimpleButton();
            ((System.ComponentModel.ISupportInitialize)(this.tabControlMain)).BeginInit();
            this.tabControlMain.SuspendLayout();
            this.tabPageRolePermissions.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridControlRolePermissions)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridViewRolePermissions)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.panelRoleSelection)).BeginInit();
            this.panelRoleSelection.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.cmbRoles.Properties)).BeginInit();
            this.tabPageUserPermissions.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridControlUserPermissions)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridViewUserPermissions)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.panelUserSelection)).BeginInit();
            this.panelUserSelection.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.cmbUsers.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.panelButtons)).BeginInit();
            this.panelButtons.SuspendLayout();
            this.SuspendLayout();
            // 
            // tabControlMain
            // 
            this.tabControlMain.Dock = System.Windows.Forms.DockStyle.Fill;
            this.tabControlMain.Location = new System.Drawing.Point(0, 0);
            this.tabControlMain.Margin = new System.Windows.Forms.Padding(4, 3, 4, 3);
            this.tabControlMain.Name = "tabControlMain";
            this.tabControlMain.SelectedTabPage = this.tabPageRolePermissions;
            this.tabControlMain.Size = new System.Drawing.Size(1384, 698);
            this.tabControlMain.TabIndex = 0;
            this.tabControlMain.TabPages.AddRange(new DevExpress.XtraTab.XtraTabPage[] {
            this.tabPageRolePermissions,
            this.tabPageUserPermissions});
            // 
            // tabPageRolePermissions
            // 
            this.tabPageRolePermissions.Controls.Add(this.gridControlRolePermissions);
            this.tabPageRolePermissions.Controls.Add(this.panelRoleSelection);
            this.tabPageRolePermissions.Margin = new System.Windows.Forms.Padding(4, 3, 4, 3);
            this.tabPageRolePermissions.Name = "tabPageRolePermissions";
            this.tabPageRolePermissions.Size = new System.Drawing.Size(1382, 673);
            this.tabPageRolePermissions.Text = "Role Permissions";
            // 
            // gridControlRolePermissions
            // 
            this.gridControlRolePermissions.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gridControlRolePermissions.EmbeddedNavigator.Margin = new System.Windows.Forms.Padding(4, 3, 4, 3);
            this.gridControlRolePermissions.Location = new System.Drawing.Point(0, 58);
            this.gridControlRolePermissions.MainView = this.gridViewRolePermissions;
            this.gridControlRolePermissions.Margin = new System.Windows.Forms.Padding(4, 3, 4, 3);
            this.gridControlRolePermissions.Name = "gridControlRolePermissions";
            this.gridControlRolePermissions.Size = new System.Drawing.Size(1382, 615);
            this.gridControlRolePermissions.TabIndex = 1;
            this.gridControlRolePermissions.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridViewRolePermissions});
            // 
            // gridViewRolePermissions
            // 
            this.gridViewRolePermissions.DetailHeight = 404;
            this.gridViewRolePermissions.GridControl = this.gridControlRolePermissions;
            this.gridViewRolePermissions.Name = "gridViewRolePermissions";
            this.gridViewRolePermissions.OptionsEditForm.PopupEditFormWidth = 933;
            // 
            // panelRoleSelection
            // 
            this.panelRoleSelection.Controls.Add(this.cmbRoles);
            this.panelRoleSelection.Controls.Add(this.labelControl1);
            this.panelRoleSelection.Dock = System.Windows.Forms.DockStyle.Top;
            this.panelRoleSelection.Location = new System.Drawing.Point(0, 0);
            this.panelRoleSelection.Margin = new System.Windows.Forms.Padding(4, 3, 4, 3);
            this.panelRoleSelection.Name = "panelRoleSelection";
            this.panelRoleSelection.Size = new System.Drawing.Size(1382, 58);
            this.panelRoleSelection.TabIndex = 0;
            // 
            // cmbRoles
            // 
            this.cmbRoles.Location = new System.Drawing.Point(93, 17);
            this.cmbRoles.Margin = new System.Windows.Forms.Padding(4, 3, 4, 3);
            this.cmbRoles.Name = "cmbRoles";
            this.cmbRoles.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.cmbRoles.Size = new System.Drawing.Size(350, 22);
            this.cmbRoles.TabIndex = 1;
            // 
            // labelControl1
            // 
            this.labelControl1.Location = new System.Drawing.Point(18, 21);
            this.labelControl1.Margin = new System.Windows.Forms.Padding(4, 3, 4, 3);
            this.labelControl1.Name = "labelControl1";
            this.labelControl1.Size = new System.Drawing.Size(60, 15);
            this.labelControl1.TabIndex = 0;
            this.labelControl1.Text = "Select Role:";
            // 
            // tabPageUserPermissions
            // 
            this.tabPageUserPermissions.Controls.Add(this.gridControlUserPermissions);
            this.tabPageUserPermissions.Controls.Add(this.panelUserSelection);
            this.tabPageUserPermissions.Margin = new System.Windows.Forms.Padding(4, 3, 4, 3);
            this.tabPageUserPermissions.Name = "tabPageUserPermissions";
            this.tabPageUserPermissions.Size = new System.Drawing.Size(1382, 673);
            this.tabPageUserPermissions.Text = "User Permissions";
            // 
            // gridControlUserPermissions
            // 
            this.gridControlUserPermissions.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gridControlUserPermissions.EmbeddedNavigator.Margin = new System.Windows.Forms.Padding(4, 3, 4, 3);
            this.gridControlUserPermissions.Location = new System.Drawing.Point(0, 58);
            this.gridControlUserPermissions.MainView = this.gridViewUserPermissions;
            this.gridControlUserPermissions.Margin = new System.Windows.Forms.Padding(4, 3, 4, 3);
            this.gridControlUserPermissions.Name = "gridControlUserPermissions";
            this.gridControlUserPermissions.Size = new System.Drawing.Size(1382, 615);
            this.gridControlUserPermissions.TabIndex = 1;
            this.gridControlUserPermissions.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridViewUserPermissions});
            // 
            // gridViewUserPermissions
            // 
            this.gridViewUserPermissions.DetailHeight = 404;
            this.gridViewUserPermissions.GridControl = this.gridControlUserPermissions;
            this.gridViewUserPermissions.Name = "gridViewUserPermissions";
            this.gridViewUserPermissions.OptionsEditForm.PopupEditFormWidth = 933;
            // 
            // panelUserSelection
            // 
            this.panelUserSelection.Controls.Add(this.btnCopyFromRole);
            this.panelUserSelection.Controls.Add(this.btnResetUserPermissions);
            this.panelUserSelection.Controls.Add(this.cmbUsers);
            this.panelUserSelection.Controls.Add(this.labelControl2);
            this.panelUserSelection.Dock = System.Windows.Forms.DockStyle.Top;
            this.panelUserSelection.Location = new System.Drawing.Point(0, 0);
            this.panelUserSelection.Margin = new System.Windows.Forms.Padding(4, 3, 4, 3);
            this.panelUserSelection.Name = "panelUserSelection";
            this.panelUserSelection.Size = new System.Drawing.Size(1382, 58);
            this.panelUserSelection.TabIndex = 0;
            // 
            // btnCopyFromRole
            // 
            this.btnCopyFromRole.Location = new System.Drawing.Point(630, 15);
            this.btnCopyFromRole.Margin = new System.Windows.Forms.Padding(4, 3, 4, 3);
            this.btnCopyFromRole.Name = "btnCopyFromRole";
            this.btnCopyFromRole.Size = new System.Drawing.Size(140, 27);
            this.btnCopyFromRole.TabIndex = 3;
            this.btnCopyFromRole.Text = "Copy from Role";
            // 
            // btnResetUserPermissions
            // 
            this.btnResetUserPermissions.Location = new System.Drawing.Point(467, 15);
            this.btnResetUserPermissions.Margin = new System.Windows.Forms.Padding(4, 3, 4, 3);
            this.btnResetUserPermissions.Name = "btnResetUserPermissions";
            this.btnResetUserPermissions.Size = new System.Drawing.Size(140, 27);
            this.btnResetUserPermissions.TabIndex = 2;
            this.btnResetUserPermissions.Text = "Reset to Role";
            // 
            // cmbUsers
            // 
            this.cmbUsers.Location = new System.Drawing.Point(93, 17);
            this.cmbUsers.Margin = new System.Windows.Forms.Padding(4, 3, 4, 3);
            this.cmbUsers.Name = "cmbUsers";
            this.cmbUsers.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.cmbUsers.Size = new System.Drawing.Size(350, 22);
            this.cmbUsers.TabIndex = 1;
            // 
            // labelControl2
            // 
            this.labelControl2.Location = new System.Drawing.Point(18, 21);
            this.labelControl2.Margin = new System.Windows.Forms.Padding(4, 3, 4, 3);
            this.labelControl2.Name = "labelControl2";
            this.labelControl2.Size = new System.Drawing.Size(60, 15);
            this.labelControl2.TabIndex = 0;
            this.labelControl2.Text = "Select User:";
            // 
            // panelButtons
            // 
            this.panelButtons.Controls.Add(this.btnRefresh);
            this.panelButtons.Controls.Add(this.btnCancel);
            this.panelButtons.Controls.Add(this.btnSave);
            this.panelButtons.Dock = System.Windows.Forms.DockStyle.Bottom;
            this.panelButtons.Location = new System.Drawing.Point(0, 698);
            this.panelButtons.Margin = new System.Windows.Forms.Padding(4, 3, 4, 3);
            this.panelButtons.Name = "panelButtons";
            this.panelButtons.Size = new System.Drawing.Size(1384, 58);
            this.panelButtons.TabIndex = 1;
            // 
            // btnRefresh
            // 
            this.btnRefresh.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.btnRefresh.Location = new System.Drawing.Point(1092, 19);
            this.btnRefresh.Margin = new System.Windows.Forms.Padding(4, 3, 4, 3);
            this.btnRefresh.Name = "btnRefresh";
            this.btnRefresh.Size = new System.Drawing.Size(88, 27);
            this.btnRefresh.TabIndex = 2;
            this.btnRefresh.Text = "Refresh";
            // 
            // btnCancel
            // 
            this.btnCancel.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.btnCancel.Location = new System.Drawing.Point(1188, 19);
            this.btnCancel.Margin = new System.Windows.Forms.Padding(4, 3, 4, 3);
            this.btnCancel.Name = "btnCancel";
            this.btnCancel.Size = new System.Drawing.Size(88, 27);
            this.btnCancel.TabIndex = 1;
            this.btnCancel.Text = "Cancel";
            // 
            // btnSave
            // 
            this.btnSave.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.btnSave.Location = new System.Drawing.Point(1284, 19);
            this.btnSave.Margin = new System.Windows.Forms.Padding(4, 3, 4, 3);
            this.btnSave.Name = "btnSave";
            this.btnSave.Size = new System.Drawing.Size(88, 27);
            this.btnSave.TabIndex = 0;
            this.btnSave.Text = "Save";
            // 
            // PermissionManagementForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 15F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(1384, 756);
            this.Controls.Add(this.tabControlMain);
            this.Controls.Add(this.panelButtons);
            this.Margin = new System.Windows.Forms.Padding(4, 3, 4, 3);
            this.Name = "PermissionManagementForm";
            this.Text = "Permission Management";
            ((System.ComponentModel.ISupportInitialize)(this.tabControlMain)).EndInit();
            this.tabControlMain.ResumeLayout(false);
            this.tabPageRolePermissions.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridControlRolePermissions)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridViewRolePermissions)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.panelRoleSelection)).EndInit();
            this.panelRoleSelection.ResumeLayout(false);
            this.panelRoleSelection.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.cmbRoles.Properties)).EndInit();
            this.tabPageUserPermissions.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridControlUserPermissions)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridViewUserPermissions)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.panelUserSelection)).EndInit();
            this.panelUserSelection.ResumeLayout(false);
            this.panelUserSelection.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.cmbUsers.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.panelButtons)).EndInit();
            this.panelButtons.ResumeLayout(false);
            this.ResumeLayout(false);

        }

        #endregion

        private DevExpress.XtraTab.XtraTabControl tabControlMain;
        private DevExpress.XtraTab.XtraTabPage tabPageRolePermissions;
        private DevExpress.XtraTab.XtraTabPage tabPageUserPermissions;
        private DevExpress.XtraGrid.GridControl gridControlRolePermissions;
        private DevExpress.XtraGrid.Views.Grid.GridView gridViewRolePermissions;
        private DevExpress.XtraEditors.PanelControl panelRoleSelection;
        private DevExpress.XtraEditors.ComboBoxEdit cmbRoles;
        private DevExpress.XtraEditors.LabelControl labelControl1;
        private DevExpress.XtraGrid.GridControl gridControlUserPermissions;
        private DevExpress.XtraGrid.Views.Grid.GridView gridViewUserPermissions;
        private DevExpress.XtraEditors.PanelControl panelUserSelection;
        private DevExpress.XtraEditors.ComboBoxEdit cmbUsers;
        private DevExpress.XtraEditors.LabelControl labelControl2;
        private DevExpress.XtraEditors.PanelControl panelButtons;
        private DevExpress.XtraEditors.SimpleButton btnRefresh;
        private DevExpress.XtraEditors.SimpleButton btnCancel;
        private DevExpress.XtraEditors.SimpleButton btnSave;
        private DevExpress.XtraEditors.SimpleButton btnResetUserPermissions;
        private DevExpress.XtraEditors.SimpleButton btnCopyFromRole;
    }
}
