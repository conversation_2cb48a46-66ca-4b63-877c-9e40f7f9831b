
# ProManage RBAC Implementation Plan
## Clean 2-Level Permission System

> **Final Implementation Plan** - Ready for 8-12 hours implementation

---

## 1. Database Structure (Ready ✅)

### Core Tables:

#### 1.1 roles Table
```sql
CREATE TABLE roles (
    role_id SERIAL PRIMARY KEY,
    role_name VARCHAR(50) UNIQUE NOT NULL,
    description TEXT,
    is_active BOOLEAN DEFAULT true,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### 1.2 role_permissions Table
```sql
CREATE TABLE role_permissions (
    permission_id SERIAL PRIMARY KEY,
    role_id INTEGER REFERENCES roles(role_id) ON DELETE CASCADE,
    form_name VARCHAR(100) NOT NULL,
    read_permission BOOLEAN DEFAULT false,
    new_permission BOOLEAN DEFAULT false,
    edit_permission BOOLEAN DEFAULT false,
    delete_permission BOOLEAN DEFAULT false,
    print_permission BOOLEAN DEFAULT false,
    UNIQUE(role_id, form_name)
);
```

#### 1.3 user_permissions Table
```sql
CREATE TABLE user_permissions (
    permission_id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(user_id) ON DELETE CASCADE,
    form_name VARCHAR(100) NOT NULL,
    read_permission BOOLEAN NULL,    -- NULL = inherit from role
    new_permission BOOLEAN NULL,     -- NULL = inherit from role
    edit_permission BOOLEAN NULL,    -- NULL = inherit from role
    delete_permission BOOLEAN NULL,  -- NULL = inherit from role
    print_permission BOOLEAN NULL,   -- NULL = inherit from role
    UNIQUE(user_id, form_name)
);
```

#### 1.4 global_permissions Table (CORRECTED)
```sql
CREATE TABLE global_permissions (
    permission_id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(user_id) ON DELETE CASCADE UNIQUE,
    global_read_permission BOOLEAN DEFAULT false,
    global_new_permission BOOLEAN DEFAULT false,
    global_edit_permission BOOLEAN DEFAULT false,
    global_delete_permission BOOLEAN DEFAULT false,
    global_print_permission BOOLEAN DEFAULT false
);
```

**CORRECTION NOTES:**
- Renamed columns from user management focus to general form operations
- Global permissions control ribbon button access across ALL forms
- These act as first-level filters before form-specific permissions

#### 1.5 users Table Enhancement
```sql
-- Add role_id to existing users table
ALTER TABLE users ADD COLUMN role_id INTEGER REFERENCES roles(role_id);
```

### Permission Logic (CORRECTED):
```
1. Check global_permissions (first-level filter)
   - If global permission = FALSE → Deny access
   - If global permission = TRUE → Continue to step 2
2. Check user_permissions (if not NULL, use it)
3. Else use role_permissions
4. Both global AND form-specific permissions must be TRUE for access
```

**CORRECTION NOTES:**
- Global permissions now act as first-level filters
- Control ribbon button visibility/enablement across ALL forms
- Form-specific permissions are secondary filters

---

## 2. File Structure

### Complete RBAC System File Organization:

```
ProManage/
├── Modules/
│   ├── Services/
│   │   ├── PermissionService.cs              # Core permission logic
│   │   └── FormDiscoveryService.cs           # Auto form detection
│   ├── Data/
│   │   └── Permissions/
│   │       ├── PermissionRepository.cs       # Database operations
│   │       └── RoleRepository.cs             # Role management
│   ├── Models/
│   │   └── Permissions/
│   │       ├── PermissionModels.cs           # Data models
│   │       ├── RoleModel.cs                  # Role model
│   │       └── UserPermissionModel.cs       # User permission model
│   ├── Helpers/
│   │   └── Permissions/
│   │       ├── PermissionHelper.cs           # Utility methods
│   │       └── PermissionConstants.cs        # Constants
│   ├── Procedures/
│   │   └── Permissions/
│   │       ├── PermissionQueries.sql         # SQL queries
│   │       ├── RoleQueries.sql               # Role queries
│   │       └── SetupQueries.sql              # Initial setup
│   └── Config/
│       └── FormsConfig.json                  # Form configuration
├── Forms/
│   └── MainForms/
│       ├── PermissionManagementForm.cs       # 2-tab permission UI (CORRECTED)
│       ├── PermissionManagementForm.Designer.cs
│       ├── PermissionManagementForm.resx
│       ├── RoleManagementForm.cs             # NEW: Role creation and management
│       ├── RoleManagementForm.Designer.cs
│       ├── RoleManagementForm.resx
│       ├── RoleMasterForm.cs                 # Enhanced role management
│       ├── UserMasterForm.cs                 # Enhanced with integrated global permissions
│       └── [Other existing MainForms...]
│   └── Dialogs/
│       ├── RoleCreateEditDialog.cs           # NEW: Role creation dialog
│       ├── RoleCreateEditDialog.Designer.cs
│       └── RoleCreateEditDialog.resx
└── docs/
    └── RBAC-Database-Setup.sql               # Complete setup script
```

### Key File Purposes:

#### Core Services:
- **PermissionService.cs**: Main permission checking logic
- **FormDiscoveryService.cs**: Auto-detect forms in MainForms folder

#### Data Layer:
- **PermissionRepository.cs**: All database operations for permissions
- **RoleRepository.cs**: Role CRUD operations

#### Models:
- **PermissionModels.cs**: Data transfer objects
- **RoleModel.cs**: Role entity model
- **UserPermissionModel.cs**: User permission entity

#### UI Forms (CORRECTED):
- **PermissionManagementForm.cs**: 2-tab permission management interface (Role & User Permissions only)
- **Enhanced RoleMasterForm.cs**: Role creation and permission assignment
- **Enhanced UserMasterForm.cs**: User permissions display with integrated global permissions
- **RoleManagementForm.cs**: NEW - Comprehensive role creation and management system

#### Configuration:
- **FormsConfig.json**: Form metadata and configuration
- **PermissionConstants.cs**: System constants and enums

---

## 3. Implementation Timeline (8-12 Hours Total)

### Phase 1: Core Permission Service (2-3 hours)
**Files to Create:**
- `Modules/Services/PermissionService.cs` - Main permission logic
- `Modules/Config/FormsConfig.json` - Forms configuration
- `Modules/Models/PermissionModels.cs` - Data models

**Key Methods:**
```csharp
bool HasPermission(userId, formName, permissionType)
bool HasGlobalPermission(userId, globalPermissionType)
List<string> GetVisibleForms(userId)
```

### Phase 2: Permission Management UI (3-4 hours) - CORRECTED
**Files to Create/Modify:**
- `Forms/MainForms/PermissionManagementForm.cs` - 2-tab permission UI (CORRECTED)
- Tab 1: Role Permissions Grid
- Tab 2: User Permissions Grid
- `Forms/MainForms/RoleManagementForm.cs` - NEW: Role creation and management
- `Forms/MainForms/UserMasterForm.cs` - MODIFY: Integrate global permissions into existing tab

### Phase 3: Form Integration (2-3 hours)
**Files to Update:**
- `Forms/MainFrame.cs` - Ribbon filtering by permissions
- `Forms/UserMasterForm.cs` - Enhanced permissions tab
- All business forms - Add permission checks

### Phase 4: Testing & Polish (1-2 hours)
- Test all permission scenarios
- Bug fixes and refinements

---

## 3. Adding New Roles

### Where to Add New Roles:
**Database Location:** `roles` table
**UI Location:** `Forms/MainForms/RoleMasterForm.cs` (already exists but needs enhancement)

### Implementation:
```sql
-- Add new role to database
INSERT INTO roles (role_name, description, is_active)
VALUES ('CustomRole', 'Custom role description', true);

-- Automatically create default permissions for new role
INSERT INTO role_permissions (role_id, form_name, read_permission, new_permission, edit_permission, delete_permission, print_permission)
SELECT r.role_id, f.form_name, false, false, false, false, false
FROM roles r
CROSS JOIN (SELECT DISTINCT form_name FROM role_permissions) f
WHERE r.role_name = 'CustomRole';
```

### RoleMasterForm Enhancement:
- Add role creation/editing functionality
- Grid to manage role permissions
- Auto-populate permissions for new roles

---

## 4. MainForms Folder Focus

### Current MainForms:
```
Forms/MainForms/
├── DatabaseForm.cs
├── ParametersForm.cs
├── RoleMasterForm.cs
├── SQLQueryForm.cs
├── UserManagementListForm.cs
└── UserMasterForm.cs
```

### Permission System Scope:
**ONLY** forms in `Forms/MainForms/` will be included in the permission system.
- Other folders (`EntryForms`, `ChildForms`, `ReusableForms`) are excluded
- This keeps the permission system focused and manageable

---

## 5. Automatic Form Detection System

### Auto-Discovery Script:
**File:** `Modules/Services/FormDiscoveryService.cs`

```csharp
public static class FormDiscoveryService
{
    public static void SyncFormsWithDatabase()
    {
        // 1. Scan MainForms folder for .cs files
        var mainFormsPath = Path.Combine(Application.StartupPath, "Forms", "MainForms");
        var formFiles = Directory.GetFiles(mainFormsPath, "*.cs")
            .Where(f => !f.EndsWith(".Designer.cs"))
            .Select(f => Path.GetFileNameWithoutExtension(f))
            .ToList();

        // 2. Get existing forms from database
        var existingForms = GetFormsFromDatabase();

        // 3. Add new forms to permission system
        var newForms = formFiles.Except(existingForms).ToList();
        foreach (var formName in newForms)
        {
            AddFormToPermissionSystem(formName);
        }

        // 4. Remove deleted forms from permission system
        var deletedForms = existingForms.Except(formFiles).ToList();
        foreach (var formName in deletedForms)
        {
            RemoveFormFromPermissionSystem(formName);
        }
    }
}
```

### Database Auto-Sync:
```sql
-- Add new form to all roles with default permissions (false)
INSERT INTO role_permissions (role_id, form_name, read_permission, new_permission, edit_permission, delete_permission, print_permission)
SELECT role_id, @formName, false, false, false, false, false
FROM roles
WHERE NOT EXISTS (
    SELECT 1 FROM role_permissions
    WHERE role_id = roles.role_id AND form_name = @formName
);

-- Remove form from permission system
DELETE FROM user_permissions WHERE form_name = @formName;
DELETE FROM role_permissions WHERE form_name = @formName;
```

### Integration Points:
- Run `FormDiscoveryService.SyncFormsWithDatabase()` on application startup
- Add menu option in admin tools to manually sync
- Log all changes for audit trail

---

## 6. Core Permission Service Logic

### Permission Resolution:
```csharp
public bool HasPermission(int userId, string formName, string permissionType)
{
    // 1. Check user override (NULL = inherit from role)
    var userPerm = GetUserPermission(userId, formName, permissionType);
    if (userPerm.HasValue) return userPerm.Value;

    // 2. Check role permission
    return GetRolePermission(userId, formName, permissionType);
}

public bool HasGlobalPermission(int userId, string permissionType)
{
    // Check global_permissions table
    return GetGlobalPermission(userId, permissionType);
}
```

### Database Queries:
```sql
-- Get user permission (returns NULL if inherit from role)
SELECT read_permission FROM user_permissions
WHERE user_id = @userId AND form_name = @formName;

-- Get role permission
SELECT rp.read_permission FROM role_permissions rp
JOIN users u ON u.role_id = rp.role_id
WHERE u.user_id = @userId AND rp.form_name = @formName;

-- Get global permission (CORRECTED)
SELECT global_read_permission FROM global_permissions
WHERE user_id = @userId;
```

---

## 7. Permission Management UI (2 Tabs) - CORRECTED

### Tab 1: Role Permissions
- Role dropdown → Load role_permissions for selected role
- Grid: Form Name | Read | New | Edit | Delete | Print
- Save to role_permissions table

### Tab 2: User Permissions
- User dropdown → Load user_permissions + role_permissions
- Grid shows: Inherited (gray) vs Override (blue)
- Save to user_permissions table (NULL = inherit)

### Global Permissions Management - MOVED TO UserMasterForm
- **CORRECTION**: Global permissions are now managed in UserMasterForm's existing permission tab
- Global permission controls integrated into existing permission tab
- Controls ribbon button access across ALL forms as first-level filters

---

## 8. Form Integration

### MainFrame Ribbon Filtering:
```csharp
private void FilterRibbonByPermissions()
{
    var userId = UserManager.Instance.CurrentUser.UserId;

    btnEstimate.Visibility = HasPermission(userId, "EstimateForm", "read")
        ? BarItemVisibility.Always : BarItemVisibility.Never;
    btnUserManagement.Visibility = HasPermission(userId, "UserMasterForm", "read")
        ? BarItemVisibility.Always : BarItemVisibility.Never;
}
```

### Form Permission Checks:
```csharp
private void EstimateForm_Load(object sender, EventArgs e)
{
    var userId = UserManager.Instance.CurrentUser.UserId;

    // Check form access
    if (!PermissionService.HasPermission(userId, "EstimateForm", "read"))
    {
        this.Close();
        return;
    }

    // Set button states
    btnNew.Enabled = PermissionService.HasPermission(userId, "EstimateForm", "new");
    btnSave.Enabled = PermissionService.HasPermission(userId, "EstimateForm", "edit");
    btnDelete.Enabled = PermissionService.HasPermission(userId, "EstimateForm", "delete");
    btnPrint.Enabled = PermissionService.HasPermission(userId, "EstimateForm", "print");
}
```

---

## 9. UserMasterForm Integration - CORRECTED

### Enhanced Permissions Tab (CORRECTED):
- **CORRECTION**: Remove separate global permissions tab (xtraTabPage2)
- Integrate global permission controls into existing permission tab
- Show effective permissions (role + user overrides)
- Add "Manage Permissions" button → Opens PermissionManagementForm
- Visual indicators: Green (role), Blue (override), Red (denied)

### Global Permissions Integration (CORRECTED):
```csharp
private void UserMasterForm_Load(object sender, EventArgs e)
{
    // Check global permissions for button states
    var userId = UserManager.Instance.CurrentUser.UserId;
    btnSave.Enabled = PermissionService.HasGlobalPermission(userId, "global_edit_permission");
    btnNew.Enabled = PermissionService.HasGlobalPermission(userId, "global_new_permission");
    btnDelete.Enabled = PermissionService.HasGlobalPermission(userId, "global_delete_permission");
}
```

### Global Permission Controls Layout:
```
[Existing Permission Controls]

┌─ Global Permissions ─────────────────────┐
│ These control ribbon access across ALL   │
│ forms in the application                 │
│                                          │
│ □ Global Read Access    □ Global New     │
│ □ Global Edit Access    □ Global Delete  │
│ □ Global Print Access                    │
└──────────────────────────────────────────┘
```

---

## 10. Role Creation and Management System - NEW REQUIREMENT

### Current Problem:
- No comprehensive role creation functionality in current implementation
- RoleMasterForm has basic NewRole() method but incomplete workflow
- Missing role management UI and validation

### Solution: RoleManagementForm
**New File:** `Forms/MainForms/RoleManagementForm.cs`

#### Features:
- MDI child form with ribbon interface
- Role listing grid: Role Name | Description | Active | Created Date | User Count
- Ribbon buttons: New Role | Edit Role | Delete Role | Copy Permissions | Refresh
- Double-click row to edit role
- Context menu with operations

#### Role Creation Dialog:
**New File:** `Forms/Dialogs/RoleCreateEditDialog.cs`

```
┌─────────────────────────────────────┐
│ Create New Role                     │
├─────────────────────────────────────┤
│ Role Name: [________________]       │
│                                     │
│ Description:                        │
│ ┌─────────────────────────────────┐ │
│ │ [Multi-line text area]          │ │
│ └─────────────────────────────────┘ │
│                                     │
│ □ Active Role                       │
│                                     │
│ ○ No permissions (recommended)      │
│ ○ Copy from: [Select Role ▼]        │
│                                     │
│           [Save] [Cancel]           │
└─────────────────────────────────────┘
```

#### Database Operations:
```csharp
// New methods in PermissionDatabaseService.cs
public static int CreateRole(RoleCreateRequest request)
public static bool UpdateRole(RoleUpdateRequest request)
public static bool DeleteRole(int roleId)
public static bool IsRoleInUse(int roleId)
public static List<Role> GetAllRolesWithUserCount()
public static bool CopyRolePermissions(int sourceRoleId, int targetRoleId)
```

#### Validation Rules:
- Role names must be unique (case-insensitive)
- Role names cannot be empty or whitespace
- Cannot delete roles that have users assigned
- Cannot delete system roles (Administrator, Manager, User, ReadOnly)

---

## 11. Implementation Priority

### ✅ **Ready to Start:**
1. **Database structure confirmed** - All tables ready (with corrections applied)
2. **Permission logic defined** - Simple 2-level system with global permissions as first-level filters
3. **UI design finalized** - 2-tab approach (CORRECTED)

### **Next Steps (CORRECTED):**
1. Apply database schema corrections (Phase 1)
2. Create PermissionService.cs with global permission logic (Phase 2)
3. Create corrected 2-tab PermissionManagementForm (Phase 3)
4. Create RoleManagementForm for role creation (Phase 4)
5. Correct UserMasterForm global permissions integration (Phase 5)
6. Integrate with existing forms (Phase 6)
7. Testing and polish (Phase 7)

**The corrected 2-level concept with global permissions is perfect!** Clean, simple, and covers all real-world needs.

---

## 12. Summary - CORRECTED

The RBAC system is now corrected and focused:

### Key Features (CORRECTED):
1. **Role Management**: Add/edit roles via NEW RoleManagementForm with comprehensive CRUD operations
2. **Auto Form Detection**: Automatic sync of MainForms with permission system
3. **3-Level Permissions**: Global permissions (first-level filter) + Role permissions + User overrides
4. **Global Permission Integration**: Managed in UserMasterForm's existing permission tab
5. **Corrected UI**: 2-tab PermissionManagementForm (Role & User permissions only)
6. **Simple Timeline**: 10-14 hours total implementation (increased due to corrections)
7. **Focused Scope**: Only MainForms included in permission system

### Next Steps (CORRECTED):
Ready to implement the corrected permission system with:
- Database schema corrections (global_permissions table)
- PermissionService.cs with global permission logic
- Corrected UserMasterForm.cs (remove separate global tab, integrate into existing)
- Corrected PermissionManagementForm.cs (2 tabs only)
- NEW RoleManagementForm.cs with role creation functionality
- NEW RoleCreateEditDialog.cs for role creation
- FormDiscoveryService.cs
- MainFrame.cs integration with global permission filtering

### Correction Summary:
1. **UserMasterForm**: Remove separate global permissions tab, integrate into existing permission tab
2. **PermissionManagementForm**: Remove global permissions tab, keep only 2 tabs
3. **Role Creation**: Add comprehensive role management system
4. **Database Schema**: Rename global_permissions columns to general operations
5. **Permission Logic**: Global permissions as first-level filters

The corrected system is clean, comprehensive, and perfectly suited for a .NET application with proper role management and global permission controls.

